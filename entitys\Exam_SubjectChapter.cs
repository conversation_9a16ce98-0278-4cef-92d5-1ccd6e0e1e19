using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace ImportNewChapterData.entitys
{
    /// <summary>
  /// 章节信息表
  /// </summary>
  [Table("Exam_SubjectChapter")]
  public class Exam_SubjectChapter
  {

      /// <summary>
      /// 章节ID
      /// </summary>
      [Key, Column(Order = 1)]
      public String Id { get; set; } = string.Empty;

      /// <summary>
      /// ParentId
      /// </summary>
      public String ParentId { get; set; } = string.Empty;

      /// <summary>
      /// 章节名称
      /// </summary>
      public String ChapterName { get; set; } = string.Empty;

      /// <summary>
      /// 排序
      /// </summary>
      public Int32? OrderId { get; set; }

      /// <summary>
      /// 学科ID
      /// </summary>
      public String SubjectId { get; set; } = string.Empty;

      /// <summary>
      /// 学期
      /// </summary>
      public Int32? WeekId { get; set; }

      /// <summary>
      /// 小节 章节编码
      /// </summary>
      public String IDNum { get; set; } = string.Empty;

      /// <summary>
      /// 必修
      /// </summary>
      public Int32? IsMajor { get; set; }

      /// <summary>
      /// 版本
      /// </summary>
      public int? Versions { get; set; }

      /// <summary>
      ///
      /// </summary>
      public string? OriginalId { get; set; }

      /// <summary>
      /// 教材Id
      /// </summary>
      public string? TextbookId { get; set; }

      /// <summary>
      /// 备注
      /// </summary>
      public string? Remark { get; set; }
  }
}