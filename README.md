# 章节数据导入工具

这是一个用于将Excel表格中的章节数据导入到`Exam_SubjectChapter`数据库表的Windows桌面应用程序。

## 功能特性

- ✅ 支持Excel (.xlsx) 文件导入
- ✅ 自动处理单元和章节的层级关系
- ✅ 重复数据检测和跳过
- ✅ 事务处理确保数据一致性
- ✅ 详细的导入日志和进度显示
- ✅ 友好的用户界面

## 数据结构说明

### Excel文件格式要求

Excel文件应包含以下列（按顺序）：

| 列 | 字段名 | 说明 | 示例 |
|---|--------|------|------|
| A | 单元名称 | 单元的名称 | 第1单元 100以内数的加减法（二） |
| B | 章节名称 | 章节的名称 | 不退位减法 |
| C | IDNum | 章节编号 | 2.1.1.1 |
| D | SubjectId | 学科ID | 2 |
| E | WeekId | 周ID | 1 |
| F | Versions | 版本号 | 2025 |
| G | OrderId | 排序ID | 1 |

### 数据关系

- **单元（Unit）**：每个单元的`ParentId`都是`2`
- **章节（Chapter）**：每个章节的`ParentId`是对应单元的ID
- **重复检测**：根据`Versions`、`SubjectId`、`IDNum`判断是否重复

## 使用方法

1. **启动程序**：运行`ImportNewChapterData.exe`

2. **创建或选择Excel文件**：
   - **创建示例文件**：右键点击"选择文件"按钮，选择"创建示例文件"，保存示例Excel文件
   - **选择现有文件**：点击"选择文件"按钮，选择包含章节数据的Excel文件（.xlsx格式）

3. **开始导入**：
   - 点击"开始导入"按钮
   - 确认导入对话框
   - 等待导入完成

4. **查看结果**：
   - 导入日志会显示详细的处理过程
   - 成功导入的单元和章节数量
   - 跳过的行及原因

### 示例文件说明

程序提供了创建示例Excel文件的功能：
- 包含3个单元，共14个章节的示例数据
- 展示了正确的数据格式和结构
- 可以直接用于测试导入功能

## 数据库配置

程序使用以下数据库连接字符串：
```
Server=*************;Database=YouwoEduPlatfrom;User ID=sa_dev;Password=*************;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=false;TrustServerCertificate=True;
```

## 技术栈

- **.NET 8.0** - 应用程序框架
- **Windows Forms** - 用户界面
- **SqlSugarCore** - ORM框架
- **NPOI** - Excel文件处理（完全免费开源）

## 项目结构

```
ImportNewChapterData/
├── entitys/
│   └── Exam_SubjectChapter.cs     # 数据库实体类
├── Services/
│   └── ChapterImportService.cs    # 导入服务类
├── Form1.cs                       # 主窗体逻辑
├── Form1.Designer.cs              # 主窗体设计
├── Program.cs                     # 程序入口
└── README.md                      # 说明文档
```

## 注意事项

1. **Excel文件格式**：确保Excel文件格式正确，第一行为标题行
2. **数据完整性**：必填字段不能为空（单元名称、章节名称、IDNum、SubjectId、Versions）
3. **网络连接**：确保能够连接到指定的数据库服务器
4. **权限要求**：确保数据库用户有足够的权限进行插入操作
5. **备份建议**：导入前建议备份数据库

## 错误处理

程序会自动处理以下情况：
- Excel文件格式错误
- 数据库连接失败
- 数据格式不正确
- 重复数据跳过
- 事务回滚

所有错误信息都会在日志中详细显示。

## 版本信息

- **版本**：v1.2
- **开发时间**：2025年8月
- **开发者**：AI Assistant

## 许可证

本项目使用NPOI开源库，完全免费无需许可证。
