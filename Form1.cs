using ImportNewChapterData.Services;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace ImportNewChapterData
{
    public partial class Form1 : Form
    {
        private readonly string _connectionString = "Server=***************;Database=YouwoEduPlatfrom;User ID=dev;Password=***$qwerASDFzxcv;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=True;TrustServerCertificate=True;";
        private ChapterImportService _importService;

        public Form1()
        {
            InitializeComponent();
            _importService = new ChapterImportService(_connectionString);
            InitializeForm();
        }

        private void InitializeForm()
        {
            // 设置窗体标题和图标
            this.Text = "章节数据导入工具 v1.0";

            // 初始化日志
            AppendLog("系统初始化完成，请选择Excel文件开始导入。");
            AppendLog("支持的Excel格式：.xlsx");
            AppendLog("Excel文件结构：单元名称 | 章节名称 | IDNum | SubjectId | WeekId | Versions | OrderId");
            AppendLog("提示：如需示例文件，请右键点击'选择文件'按钮");
            AppendLog("----------------------------------------");
        }

        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Title = "选择Excel文件",
                Filter = "Excel文件 (*.xlsx)|*.xlsx|所有文件 (*.*)|*.*",
                FilterIndex = 1,
                RestoreDirectory = true
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtFilePath.Text = openFileDialog.FileName;
                btnImport.Enabled = true;
                AppendLog($"已选择文件：{openFileDialog.FileName}");
            }
        }

        private async void btnImport_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtFilePath.Text))
            {
                MessageBox.Show("请先选择Excel文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!File.Exists(txtFilePath.Text))
            {
                MessageBox.Show("选择的文件不存在！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 确认导入
            var confirmResult = MessageBox.Show(
                "确定要开始导入数据吗？\n\n注意：\n1. 导入过程中请勿关闭程序\n2. 重复数据将被跳过\n3. 导入完成后会显示详细结果",
                "确认导入",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (confirmResult != DialogResult.Yes)
            {
                return;
            }

            // 禁用按钮，开始导入
            btnSelectFile.Enabled = false;
            btnImport.Enabled = false;
            progressBar.Style = ProgressBarStyle.Marquee;
            progressBar.MarqueeAnimationSpeed = 50;

            AppendLog("开始导入数据...");
            AppendLog($"文件路径：{txtFilePath.Text}");

            try
            {
                var result = await _importService.ImportFromExcelAsync(txtFilePath.Text);

                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 100;

                if (result.Success)
                {
                    AppendLog("✓ 导入成功！");
                    AppendLog($"✓ 新增单元：{result.UnitsInserted} 个");
                    AppendLog($"✓ 新增章节：{result.ChaptersInserted} 个");

                    if (result.SkippedRows.Any())
                    {
                        AppendLog($"⚠ 跳过行数：{result.SkippedRows.Count} 行");
                        AppendLog("跳过的行详情：");
                        foreach (var skippedRow in result.SkippedRows)
                        {
                            AppendLog($"  - {skippedRow}");
                        }
                    }

                    MessageBox.Show(result.Message, "导入成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    AppendLog("✗ 导入失败！");
                    AppendLog($"✗ 错误信息：{result.Message}");

                    if (result.SkippedRows.Any())
                    {
                        AppendLog("错误详情：");
                        foreach (var skippedRow in result.SkippedRows)
                        {
                            AppendLog($"  - {skippedRow}");
                        }
                    }

                    MessageBox.Show(result.Message, "导入失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 0;

                AppendLog("✗ 导入过程中发生异常！");
                AppendLog($"✗ 异常信息：{ex.Message}");

                MessageBox.Show($"导入过程中发生异常：{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnSelectFile.Enabled = true;
                btnImport.Enabled = true;
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 0;

                AppendLog("----------------------------------------");
            }
        }

        private void createSampleFileToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存示例Excel文件",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx",
                    FileName = "示例章节数据.xlsx",
                    RestoreDirectory = true
                };

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    CreateSampleExcelFile(saveFileDialog.FileName);
                    txtFilePath.Text = saveFileDialog.FileName;
                    btnImport.Enabled = true;
                    AppendLog($"✓ 示例文件创建成功：{saveFileDialog.FileName}");
                    MessageBox.Show("示例Excel文件创建成功！\n\n文件包含3个单元，共14个章节的示例数据。", "创建成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                AppendLog($"✗ 创建示例文件失败：{ex.Message}");
                MessageBox.Show($"创建示例文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateSampleExcelFile(string filePath)
        {
            var workbook = new XSSFWorkbook();
            var worksheet = workbook.CreateSheet("章节数据");

            // 设置标题行
            var headerRow = worksheet.CreateRow(0);
            headerRow.CreateCell(0).SetCellValue("单元");
            headerRow.CreateCell(1).SetCellValue("章节");
            headerRow.CreateCell(2).SetCellValue("IDNum");
            headerRow.CreateCell(3).SetCellValue("SubjectId");
            headerRow.CreateCell(4).SetCellValue("WeekId");
            headerRow.CreateCell(5).SetCellValue("Versions");
            headerRow.CreateCell(6).SetCellValue("OrderId");

            // 添加示例数据
            var sampleData = new object[,]
            {
                {"第1单元 100以内数的加减法（二）", "不退位减法", "2.1.1.1", "2", "1", "2025", "1"},
                {"第1单元 100以内数的加减法（二）", "退位减法", "2.1.1.2", "2", "1", "2025", "2"},
                {"第1单元 100以内数的加减法（二）", "小练习（1）", "2.1.1.3", "2", "1", "2025", "3"},
                {"第1单元 100以内数的加减法（二）", "加与减", "2.1.1.4", "2", "1", "2025", "4"},
                {"第1单元 100以内数的加减法（二）", "小练习（2）", "2.1.1.5", "2", "1", "2025", "5"},
                {"第1单元 100以内数的加减法（二）", "计算算算（1）", "2.1.1.6", "2", "1", "2025", "6"},
                {"第1单元 100以内数的加减法（二）", "小练习（3）", "2.1.1.7", "2", "1", "2025", "7"},
                {"第2单元 表内乘法和表内除法", "乘法口诀", "2.1.2.1", "2", "1", "2025", "1"},
                {"第2单元 表内乘法和表内除法", "乘法应用", "2.1.2.2", "2", "1", "2025", "2"},
                {"第2单元 表内乘法和表内除法", "除法基础", "2.1.2.3", "2", "1", "2025", "3"},
                {"第2单元 表内乘法和表内除法", "除法应用", "2.1.2.4", "2", "1", "2025", "4"},
                {"第3单元 图形与几何", "认识图形", "2.1.3.1", "2", "1", "2025", "1"},
                {"第3单元 图形与几何", "图形分类", "2.1.3.2", "2", "1", "2025", "2"},
                {"第3单元 图形与几何", "图形组合", "2.1.3.3", "2", "1", "2025", "3"}
            };

            // 填充数据
            for (int i = 0; i < sampleData.GetLength(0); i++)
            {
                var dataRow = worksheet.CreateRow(i + 1); // 从第1行开始（第0行是标题）
                for (int j = 0; j < sampleData.GetLength(1); j++)
                {
                    dataRow.CreateCell(j).SetCellValue(sampleData[i, j].ToString());
                }
            }

            // 设置列宽（NPOI中列宽单位是1/256字符宽度）
            worksheet.SetColumnWidth(0, 30 * 256); // 单元名称
            worksheet.SetColumnWidth(1, 20 * 256); // 章节名称
            worksheet.SetColumnWidth(2, 15 * 256); // IDNum
            worksheet.SetColumnWidth(3, 12 * 256); // SubjectId
            worksheet.SetColumnWidth(4, 10 * 256); // WeekId
            worksheet.SetColumnWidth(5, 12 * 256); // Versions
            worksheet.SetColumnWidth(6, 10 * 256); // OrderId

            // 设置标题行样式
            var headerStyle = workbook.CreateCellStyle();
            var headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            headerStyle.SetFont(headerFont);
            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Grey25Percent.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;

            // 应用标题样式
            var headerRowObj = worksheet.GetRow(0);
            for (int i = 0; i < 7; i++)
            {
                headerRowObj.GetCell(i).CellStyle = headerStyle;
            }

            // 保存文件
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
            workbook.Write(fileStream);
            workbook.Close();
        }

        private void AppendLog(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AppendLog), message);
                return;
            }

            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            txtLog.AppendText($"[{timestamp}] {message}\r\n");
            txtLog.SelectionStart = txtLog.Text.Length;
            txtLog.ScrollToCaret();
        }
    }
}
