using ImportNewChapterData.Services;
using OfficeOpenXml;

namespace ImportNewChapterData
{
    public partial class Form1 : Form
    {
        private readonly string _connectionString = "Server=*************;Database=YouwoEduPlatfrom;User ID=sa_dev;Password=*************;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=false;TrustServerCertificate=True;";
        private ChapterImportService _importService;

        public Form1()
        {
            InitializeComponent();
            _importService = new ChapterImportService(_connectionString);
            InitializeForm();
        }

        private void InitializeForm()
        {
            // 设置窗体标题和图标
            this.Text = "章节数据导入工具 v1.0";

            // 初始化日志
            AppendLog("系统初始化完成，请选择Excel文件开始导入。");
            AppendLog("支持的Excel格式：.xlsx");
            AppendLog("Excel文件结构：单元名称 | 章节名称 | IDNum | SubjectId | WeekId | Versions | OrderId");
            AppendLog("提示：如需示例文件，请右键点击'选择文件'按钮");
            AppendLog("----------------------------------------");
        }

        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Title = "选择Excel文件",
                Filter = "Excel文件 (*.xlsx)|*.xlsx|所有文件 (*.*)|*.*",
                FilterIndex = 1,
                RestoreDirectory = true
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtFilePath.Text = openFileDialog.FileName;
                btnImport.Enabled = true;
                AppendLog($"已选择文件：{openFileDialog.FileName}");
            }
        }

        private async void btnImport_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtFilePath.Text))
            {
                MessageBox.Show("请先选择Excel文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!File.Exists(txtFilePath.Text))
            {
                MessageBox.Show("选择的文件不存在！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 确认导入
            var confirmResult = MessageBox.Show(
                "确定要开始导入数据吗？\n\n注意：\n1. 导入过程中请勿关闭程序\n2. 重复数据将被跳过\n3. 导入完成后会显示详细结果",
                "确认导入",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (confirmResult != DialogResult.Yes)
            {
                return;
            }

            // 禁用按钮，开始导入
            btnSelectFile.Enabled = false;
            btnImport.Enabled = false;
            progressBar.Style = ProgressBarStyle.Marquee;
            progressBar.MarqueeAnimationSpeed = 50;

            AppendLog("开始导入数据...");
            AppendLog($"文件路径：{txtFilePath.Text}");

            try
            {
                var result = await _importService.ImportFromExcelAsync(txtFilePath.Text);

                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 100;

                if (result.Success)
                {
                    AppendLog("✓ 导入成功！");
                    AppendLog($"✓ 新增单元：{result.UnitsInserted} 个");
                    AppendLog($"✓ 新增章节：{result.ChaptersInserted} 个");

                    if (result.SkippedRows.Any())
                    {
                        AppendLog($"⚠ 跳过行数：{result.SkippedRows.Count} 行");
                        AppendLog("跳过的行详情：");
                        foreach (var skippedRow in result.SkippedRows)
                        {
                            AppendLog($"  - {skippedRow}");
                        }
                    }

                    MessageBox.Show(result.Message, "导入成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    AppendLog("✗ 导入失败！");
                    AppendLog($"✗ 错误信息：{result.Message}");

                    if (result.SkippedRows.Any())
                    {
                        AppendLog("错误详情：");
                        foreach (var skippedRow in result.SkippedRows)
                        {
                            AppendLog($"  - {skippedRow}");
                        }
                    }

                    MessageBox.Show(result.Message, "导入失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 0;

                AppendLog("✗ 导入过程中发生异常！");
                AppendLog($"✗ 异常信息：{ex.Message}");

                MessageBox.Show($"导入过程中发生异常：{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnSelectFile.Enabled = true;
                btnImport.Enabled = true;
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 0;

                AppendLog("----------------------------------------");
            }
        }

        private void createSampleFileToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存示例Excel文件",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx",
                    FileName = "示例章节数据.xlsx",
                    RestoreDirectory = true
                };

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    CreateSampleExcelFile(saveFileDialog.FileName);
                    txtFilePath.Text = saveFileDialog.FileName;
                    btnImport.Enabled = true;
                    AppendLog($"✓ 示例文件创建成功：{saveFileDialog.FileName}");
                    MessageBox.Show("示例Excel文件创建成功！\n\n文件包含3个单元，共14个章节的示例数据。", "创建成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                AppendLog($"✗ 创建示例文件失败：{ex.Message}");
                MessageBox.Show($"创建示例文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateSampleExcelFile(string filePath)
        {
            // 设置EPPlus许可证上下文
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("章节数据");

            // 设置标题行
            worksheet.Cells[1, 1].Value = "单元";
            worksheet.Cells[1, 2].Value = "章节";
            worksheet.Cells[1, 3].Value = "IDNum";
            worksheet.Cells[1, 4].Value = "SubjectId";
            worksheet.Cells[1, 5].Value = "WeekId";
            worksheet.Cells[1, 6].Value = "Versions";
            worksheet.Cells[1, 7].Value = "OrderId";

            // 添加示例数据
            var sampleData = new object[,]
            {
                {"第1单元 100以内数的加减法（二）", "不退位减法", "2.1.1.1", "2", "1", "2025", "1"},
                {"第1单元 100以内数的加减法（二）", "退位减法", "2.1.1.2", "2", "1", "2025", "2"},
                {"第1单元 100以内数的加减法（二）", "小练习（1）", "2.1.1.3", "2", "1", "2025", "3"},
                {"第1单元 100以内数的加减法（二）", "加与减", "2.1.1.4", "2", "1", "2025", "4"},
                {"第1单元 100以内数的加减法（二）", "小练习（2）", "2.1.1.5", "2", "1", "2025", "5"},
                {"第1单元 100以内数的加减法（二）", "计算算算（1）", "2.1.1.6", "2", "1", "2025", "6"},
                {"第1单元 100以内数的加减法（二）", "小练习（3）", "2.1.1.7", "2", "1", "2025", "7"},
                {"第2单元 表内乘法和表内除法", "乘法口诀", "2.1.2.1", "2", "1", "2025", "1"},
                {"第2单元 表内乘法和表内除法", "乘法应用", "2.1.2.2", "2", "1", "2025", "2"},
                {"第2单元 表内乘法和表内除法", "除法基础", "2.1.2.3", "2", "1", "2025", "3"},
                {"第2单元 表内乘法和表内除法", "除法应用", "2.1.2.4", "2", "1", "2025", "4"},
                {"第3单元 图形与几何", "认识图形", "2.1.3.1", "2", "1", "2025", "1"},
                {"第3单元 图形与几何", "图形分类", "2.1.3.2", "2", "1", "2025", "2"},
                {"第3单元 图形与几何", "图形组合", "2.1.3.3", "2", "1", "2025", "3"}
            };

            // 填充数据
            for (int i = 0; i < sampleData.GetLength(0); i++)
            {
                for (int j = 0; j < sampleData.GetLength(1); j++)
                {
                    worksheet.Cells[i + 2, j + 1].Value = sampleData[i, j];
                }
            }

            // 设置列宽
            worksheet.Column(1).Width = 30; // 单元名称
            worksheet.Column(2).Width = 20; // 章节名称
            worksheet.Column(3).Width = 15; // IDNum
            worksheet.Column(4).Width = 12; // SubjectId
            worksheet.Column(5).Width = 10; // WeekId
            worksheet.Column(6).Width = 12; // Versions
            worksheet.Column(7).Width = 10; // OrderId

            // 设置标题行样式
            using (var range = worksheet.Cells[1, 1, 1, 7])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
            }

            // 设置数据区域边框
            using (var range = worksheet.Cells[1, 1, sampleData.GetLength(0) + 1, 7])
            {
                range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            }

            // 保存文件
            var fileInfo = new FileInfo(filePath);
            package.SaveAs(fileInfo);
        }

        private void AppendLog(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AppendLog), message);
                return;
            }

            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            txtLog.AppendText($"[{timestamp}] {message}\r\n");
            txtLog.SelectionStart = txtLog.Text.Length;
            txtLog.ScrollToCaret();
        }
    }
}
