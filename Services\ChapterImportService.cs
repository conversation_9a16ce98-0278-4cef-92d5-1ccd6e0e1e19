using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SqlSugar;
using OfficeOpenXml;
using ImportNewChapterData.entitys;

namespace ImportNewChapterData.Services
{
    /// <summary>
    /// 章节数据导入服务
    /// </summary>
    public class ChapterImportService
    {
        private readonly ISqlSugarClient _db;
        private readonly string _connectionString;

        public ChapterImportService(string connectionString)
        {
            _connectionString = connectionString;
            _db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = connectionString,
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute
            });
        }

        /// <summary>
        /// 从Excel文件导入章节数据
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>导入结果</returns>
        public async Task<ImportResult> ImportFromExcelAsync(string filePath)
        {
            var result = new ImportResult();
            
            try
            {
                // EPPlus许可证已在Program.cs中设置
                
                using var package = new ExcelPackage(new FileInfo(filePath));
                var worksheet = package.Workbook.Worksheets[0]; // 获取第一个工作表
                
                if (worksheet == null)
                {
                    result.Success = false;
                    result.Message = "Excel文件中没有找到工作表";
                    return result;
                }

                // 获取数据行数
                int rowCount = worksheet.Dimension?.Rows ?? 0;
                if (rowCount <= 1) // 只有标题行或没有数据
                {
                    result.Success = false;
                    result.Message = "Excel文件中没有数据行";
                    return result;
                }

                var unitsToInsert = new List<Exam_SubjectChapter>();
                var chaptersToInsert = new List<Exam_SubjectChapter>();
                var unitIdMap = new Dictionary<string, string>(); // 单元名称 -> 生成的ID

                // 从第2行开始读取数据（第1行是标题）
                for (int row = 2; row <= rowCount; row++)
                {
                    try
                    {
                        var unitName = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                        var chapterName = worksheet.Cells[row, 2].Value?.ToString()?.Trim();
                        var idNum = worksheet.Cells[row, 3].Value?.ToString()?.Trim();
                        var subjectId = worksheet.Cells[row, 4].Value?.ToString()?.Trim();
                        var weekId = worksheet.Cells[row, 5].Value?.ToString()?.Trim();
                        var versions = worksheet.Cells[row, 6].Value?.ToString()?.Trim();
                        var orderId = worksheet.Cells[row, 7].Value?.ToString()?.Trim();

                        // 验证必填字段
                        if (string.IsNullOrEmpty(unitName) || string.IsNullOrEmpty(chapterName) || 
                            string.IsNullOrEmpty(idNum) || string.IsNullOrEmpty(subjectId) || 
                            string.IsNullOrEmpty(versions))
                        {
                            result.SkippedRows.Add($"第{row}行：缺少必填字段");
                            continue;
                        }

                        // 解析数值字段
                        if (!int.TryParse(weekId, out int weekIdValue))
                        {
                            weekIdValue = 1; // 默认值
                        }
                        
                        if (!int.TryParse(versions, out int versionsValue))
                        {
                            result.SkippedRows.Add($"第{row}行：版本号格式错误");
                            continue;
                        }
                        
                        if (!int.TryParse(orderId, out int orderIdValue))
                        {
                            orderIdValue = 1; // 默认值
                        }

                        // 处理单元数据
                        if (!unitIdMap.ContainsKey(unitName))
                        {
                            var unitId = Guid.NewGuid().ToString();
                            unitIdMap[unitName] = unitId;

                            // 生成单元的IDNum（去掉章节IDNum的最后一级）
                            var unitIdNum = idNum.Substring(0, idNum.LastIndexOf('.'));

                            // 检查单元是否已存在
                            var existingUnit = await _db.Queryable<Exam_SubjectChapter>()
                                .Where(x => x.ChapterName == unitName &&
                                           x.SubjectId == subjectId &&
                                           x.Versions == versionsValue &&
                                           x.ParentId == "2")
                                .FirstAsync();

                            if (existingUnit == null)
                            {
                                var unit = new Exam_SubjectChapter
                                {
                                    Id = unitId,
                                    ParentId = "2", // 单元的ParentId都为2
                                    ChapterName = unitName,
                                    SubjectId = subjectId,
                                    WeekId = weekIdValue,
                                    Versions = versionsValue,
                                    OrderId = 1, // 单元的排序从1开始
                                    IDNum = unitIdNum,
                                    IsMajor = 1, // 默认为必修
                                    OriginalId = null,
                                    TextbookId = null,
                                    Remark = null
                                };
                                unitsToInsert.Add(unit);
                            }
                            else
                            {
                                unitIdMap[unitName] = existingUnit.Id; // 使用已存在的ID
                            }
                        }

                        // 检查章节是否已存在
                        var existingChapter = await _db.Queryable<Exam_SubjectChapter>()
                            .Where(x => x.IDNum == idNum && 
                                       x.SubjectId == subjectId && 
                                       x.Versions == versionsValue)
                            .FirstAsync();

                        if (existingChapter == null)
                        {
                            var chapter = new Exam_SubjectChapter
                            {
                                Id = Guid.NewGuid().ToString(),
                                ParentId = unitIdMap[unitName], // 章节的ParentId是单元ID
                                ChapterName = chapterName,
                                SubjectId = subjectId,
                                WeekId = weekIdValue,
                                Versions = versionsValue,
                                OrderId = orderIdValue,
                                IDNum = idNum,
                                IsMajor = 1, // 默认为必修
                                OriginalId = null,
                                TextbookId = null,
                                Remark = null
                            };
                            chaptersToInsert.Add(chapter);
                        }
                        else
                        {
                            result.SkippedRows.Add($"第{row}行：章节已存在 (IDNum: {idNum})");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.SkippedRows.Add($"第{row}行：处理异常 - {ex.Message}");
                    }
                }

                // 使用事务插入数据
                await _db.Ado.BeginTranAsync();
                try
                {
                    // 先插入单元
                    if (unitsToInsert.Any())
                    {
                        await _db.Insertable(unitsToInsert).ExecuteCommandAsync();
                        result.UnitsInserted = unitsToInsert.Count;
                    }

                    // 再插入章节
                    if (chaptersToInsert.Any())
                    {
                        await _db.Insertable(chaptersToInsert).ExecuteCommandAsync();
                        result.ChaptersInserted = chaptersToInsert.Count;
                    }

                    await _db.Ado.CommitTranAsync();
                    
                    result.Success = true;
                    result.Message = $"导入完成！新增单元：{result.UnitsInserted}个，新增章节：{result.ChaptersInserted}个";
                }
                catch (Exception ex)
                {
                    await _db.Ado.RollbackTranAsync();
                    result.Success = false;
                    result.Message = $"数据库操作失败：{ex.Message}";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"导入失败：{ex.Message}";
            }

            return result;
        }
    }

    /// <summary>
    /// 导入结果
    /// </summary>
    public class ImportResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int UnitsInserted { get; set; }
        public int ChaptersInserted { get; set; }
        public List<string> SkippedRows { get; set; } = new List<string>();
    }
}
