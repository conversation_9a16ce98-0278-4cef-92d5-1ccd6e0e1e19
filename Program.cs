using OfficeOpenXml;

namespace ImportNewChapterData
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 设置EPPlus许可证（非商业用途）
            SetEPPlusLicense();

            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();
            Application.Run(new Form1());
        }

        private static void SetEPPlusLicense()
        {
            try
            {
                // 尝试设置许可证上下文（兼容不同版本的EPPlus）
                var licenseContextProperty = typeof(ExcelPackage).GetProperty("LicenseContext");
                if (licenseContextProperty != null && licenseContextProperty.CanWrite)
                {
                    // 使用反射设置LicenseContext.NonCommercial (值为1)
                    var nonCommercialValue = Enum.Parse(licenseContextProperty.PropertyType, "NonCommercial");
                    licenseContextProperty.SetValue(null, nonCommercialValue);
                }
                else
                {
                    // 尝试新的API方式
                    var licenseProperty = typeof(ExcelPackage).GetProperty("License");
                    if (licenseProperty != null && licenseProperty.CanWrite)
                    {
                        // 这里可能需要根据实际的EPPlus版本调整
                        Console.WriteLine("EPPlus License property found but may need specific implementation");
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果许可证设置失败，记录错误但不阻止程序启动
                Console.WriteLine($"Warning: Failed to set EPPlus license: {ex.Message}");
            }
        }
    }
}