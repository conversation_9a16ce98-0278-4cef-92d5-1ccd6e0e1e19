# 章节数据导入工具 - 使用说明

## 快速开始

### 1. 启动程序
双击运行 `ImportNewChapterData.exe` 文件

### 2. 创建示例文件（推荐新用户）
- 右键点击"选择文件"按钮
- 选择"创建示例文件"
- 选择保存位置，文件名建议为"示例章节数据.xlsx"
- 点击保存

### 3. 导入数据
- 点击"开始导入"按钮
- 确认导入操作
- 等待导入完成，查看日志信息

## 详细功能说明

### 界面介绍

1. **文件选择区域**
   - Excel文件路径显示框
   - "选择文件"按钮（支持右键菜单）
   - "开始导入"按钮

2. **进度显示区域**
   - 导入进度条
   - 实时状态显示

3. **日志显示区域**
   - 详细的操作日志
   - 错误信息和跳过记录
   - 导入结果统计

### Excel文件格式要求

#### 必须包含的列（按顺序）：

| 列号 | 列名 | 说明 | 示例值 | 是否必填 |
|------|------|------|--------|----------|
| A | 单元 | 单元名称 | 第1单元 100以内数的加减法（二） | ✓ |
| B | 章节 | 章节名称 | 不退位减法 | ✓ |
| C | IDNum | 章节编号 | 2.1.1.1 | ✓ |
| D | SubjectId | 学科ID | 2 | ✓ |
| E | WeekId | 周ID | 1 | 可选 |
| F | Versions | 版本号 | 2025 | ✓ |
| G | OrderId | 排序ID | 1 | 可选 |

#### 注意事项：
- 第一行必须是标题行
- 必填字段不能为空
- IDNum格式应为数字.数字.数字.数字的形式
- Versions和SubjectId必须为数字

### 数据处理逻辑

#### 单元处理
- 每个单元的ParentId自动设置为"2"
- 单元的IDNum从章节IDNum中自动提取（去掉最后一级）
- 相同单元名称只会创建一次

#### 章节处理
- 每个章节的ParentId设置为对应单元的ID
- 章节按照Excel中的顺序进行处理

#### 重复检测
根据以下字段组合判断是否重复：
- Versions（版本号）
- SubjectId（学科ID）
- IDNum（章节编号）

如果存在重复，该行数据将被跳过。

### 导入结果说明

#### 成功导入
- 显示新增单元数量
- 显示新增章节数量
- 显示总处理时间

#### 跳过记录
- 重复数据
- 必填字段为空
- 数据格式错误
- 其他验证失败

#### 错误处理
- 文件读取错误
- 数据库连接错误
- 数据插入错误
- 所有错误都会回滚，确保数据一致性

## 常见问题

### Q1: 为什么有些行被跳过了？
**A:** 可能的原因：
- 数据已存在（根据Versions、SubjectId、IDNum判断）
- 必填字段为空
- 数据格式不正确（如版本号不是数字）

### Q2: 导入失败怎么办？
**A:** 检查以下项目：
- 网络连接是否正常
- 数据库服务器是否可访问
- Excel文件格式是否正确
- 查看详细错误日志

### Q3: 程序使用什么Excel处理库？
**A:** 程序使用NPOI库处理Excel文件：
- NPOI是完全免费的开源库
- 无需任何许可证配置
- 支持.xlsx格式文件的读写

### Q4: 可以重复导入同一个文件吗？
**A:** 可以，但重复的数据会被自动跳过，只有新数据会被导入。

### Q5: 如何确认数据导入成功？
**A:**
- 查看导入日志中的成功统计
- 检查数据库中的Exam_SubjectChapter表
- 确认没有错误信息

## 技术支持

如果遇到问题，请：
1. 查看程序日志中的详细错误信息
2. 确认Excel文件格式是否符合要求
3. 检查网络和数据库连接
4. 联系技术支持并提供错误日志

## 版本历史

- **v1.2** (2025-08)
  - 🎉 **重大更新**：替换EPPlus为NPOI库
  - ✅ 完全免费，无需许可证配置
  - ✅ 解决了所有许可证相关问题
  - 🔧 优化Excel文件处理性能

- **v1.1** (2025-08)
  - 修复EPPlus 8.x许可证问题
  - 改进许可证设置机制
  - 优化错误处理

- **v1.0** (2025-08)
  - 初始版本
  - 支持Excel文件导入
  - 支持创建示例文件
  - 完整的错误处理和日志记录
