using OfficeOpenXml;
using System;
using System.IO;

namespace ImportNewChapterData.TestData
{
    /// <summary>
    /// 创建示例Excel文件的工具类
    /// </summary>
    public class CreateSampleExcel
    {
        public static void CreateSampleFile(string filePath)
        {
            // EPPlus许可证已在Program.cs中设置

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("章节数据");

            // 设置标题行
            worksheet.Cells[1, 1].Value = "单元";
            worksheet.Cells[1, 2].Value = "章节";
            worksheet.Cells[1, 3].Value = "IDNum";
            worksheet.Cells[1, 4].Value = "SubjectId";
            worksheet.Cells[1, 5].Value = "WeekId";
            worksheet.Cells[1, 6].Value = "Versions";
            worksheet.Cells[1, 7].Value = "OrderId";

            // 添加示例数据
            var sampleData = new object[,]
            {
                {"第1单元 100以内数的加减法（二）", "不退位减法", "2.1.1.1", "2", "1", "2025", "1"},
                {"第1单元 100以内数的加减法（二）", "退位减法", "2.1.1.2", "2", "1", "2025", "2"},
                {"第1单元 100以内数的加减法（二）", "小练习（1）", "2.1.1.3", "2", "1", "2025", "3"},
                {"第1单元 100以内数的加减法（二）", "加与减", "2.1.1.4", "2", "1", "2025", "4"},
                {"第1单元 100以内数的加减法（二）", "小练习（2）", "2.1.1.5", "2", "1", "2025", "5"},
                {"第1单元 100以内数的加减法（二）", "计算算算（1）", "2.1.1.6", "2", "1", "2025", "6"},
                {"第1单元 100以内数的加减法（二）", "小练习（3）", "2.1.1.7", "2", "1", "2025", "7"},
                {"第2单元 表内乘法和表内除法", "乘法口诀", "2.1.2.1", "2", "1", "2025", "1"},
                {"第2单元 表内乘法和表内除法", "乘法应用", "2.1.2.2", "2", "1", "2025", "2"},
                {"第2单元 表内乘法和表内除法", "除法基础", "2.1.2.3", "2", "1", "2025", "3"},
                {"第2单元 表内乘法和表内除法", "除法应用", "2.1.2.4", "2", "1", "2025", "4"},
                {"第3单元 图形与几何", "认识图形", "2.1.3.1", "2", "1", "2025", "1"},
                {"第3单元 图形与几何", "图形分类", "2.1.3.2", "2", "1", "2025", "2"},
                {"第3单元 图形与几何", "图形组合", "2.1.3.3", "2", "1", "2025", "3"}
            };

            // 填充数据
            for (int i = 0; i < sampleData.GetLength(0); i++)
            {
                for (int j = 0; j < sampleData.GetLength(1); j++)
                {
                    worksheet.Cells[i + 2, j + 1].Value = sampleData[i, j];
                }
            }

            // 设置列宽
            worksheet.Column(1).Width = 30; // 单元名称
            worksheet.Column(2).Width = 20; // 章节名称
            worksheet.Column(3).Width = 15; // IDNum
            worksheet.Column(4).Width = 12; // SubjectId
            worksheet.Column(5).Width = 10; // WeekId
            worksheet.Column(6).Width = 12; // Versions
            worksheet.Column(7).Width = 10; // OrderId

            // 设置标题行样式
            using (var range = worksheet.Cells[1, 1, 1, 7])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
            }

            // 设置数据区域边框
            using (var range = worksheet.Cells[1, 1, sampleData.GetLength(0) + 1, 7])
            {
                range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            }

            // 保存文件
            var fileInfo = new FileInfo(filePath);
            package.SaveAs(fileInfo);
        }
    }
}
