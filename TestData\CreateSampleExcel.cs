using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.IO;

namespace ImportNewChapterData.TestData
{
    /// <summary>
    /// 创建示例Excel文件的工具类
    /// </summary>
    public class CreateSampleExcel
    {
        public static void CreateSampleFile(string filePath)
        {
            var workbook = new XSSFWorkbook();
            var worksheet = workbook.CreateSheet("章节数据");

            // 设置标题行
            var headerRow = worksheet.CreateRow(0);
            headerRow.CreateCell(0).SetCellValue("单元");
            headerRow.CreateCell(1).SetCellValue("章节");
            headerRow.CreateCell(2).SetCellValue("IDNum");
            headerRow.CreateCell(3).SetCellValue("SubjectId");
            headerRow.CreateCell(4).SetCellValue("WeekId");
            headerRow.CreateCell(5).SetCellValue("Versions");
            headerRow.CreateCell(6).SetCellValue("OrderId");

            // 添加示例数据
            var sampleData = new object[,]
            {
                {"第1单元 100以内数的加减法（二）", "不退位减法", "2.1.1.1", "2", "1", "2025", "1"},
                {"第1单元 100以内数的加减法（二）", "退位减法", "2.1.1.2", "2", "1", "2025", "2"},
                {"第1单元 100以内数的加减法（二）", "小练习（1）", "2.1.1.3", "2", "1", "2025", "3"},
                {"第1单元 100以内数的加减法（二）", "加与减", "2.1.1.4", "2", "1", "2025", "4"},
                {"第1单元 100以内数的加减法（二）", "小练习（2）", "2.1.1.5", "2", "1", "2025", "5"},
                {"第1单元 100以内数的加减法（二）", "计算算算（1）", "2.1.1.6", "2", "1", "2025", "6"},
                {"第1单元 100以内数的加减法（二）", "小练习（3）", "2.1.1.7", "2", "1", "2025", "7"},
                {"第2单元 表内乘法和表内除法", "乘法口诀", "2.1.2.1", "2", "1", "2025", "1"},
                {"第2单元 表内乘法和表内除法", "乘法应用", "2.1.2.2", "2", "1", "2025", "2"},
                {"第2单元 表内乘法和表内除法", "除法基础", "2.1.2.3", "2", "1", "2025", "3"},
                {"第2单元 表内乘法和表内除法", "除法应用", "2.1.2.4", "2", "1", "2025", "4"},
                {"第3单元 图形与几何", "认识图形", "2.1.3.1", "2", "1", "2025", "1"},
                {"第3单元 图形与几何", "图形分类", "2.1.3.2", "2", "1", "2025", "2"},
                {"第3单元 图形与几何", "图形组合", "2.1.3.3", "2", "1", "2025", "3"}
            };

            // 填充数据
            for (int i = 0; i < sampleData.GetLength(0); i++)
            {
                var dataRow = worksheet.CreateRow(i + 1); // 从第1行开始（第0行是标题）
                for (int j = 0; j < sampleData.GetLength(1); j++)
                {
                    dataRow.CreateCell(j).SetCellValue(sampleData[i, j].ToString());
                }
            }

            // 设置列宽（NPOI中列宽单位是1/256字符宽度）
            worksheet.SetColumnWidth(0, 30 * 256); // 单元名称
            worksheet.SetColumnWidth(1, 20 * 256); // 章节名称
            worksheet.SetColumnWidth(2, 15 * 256); // IDNum
            worksheet.SetColumnWidth(3, 12 * 256); // SubjectId
            worksheet.SetColumnWidth(4, 10 * 256); // WeekId
            worksheet.SetColumnWidth(5, 12 * 256); // Versions
            worksheet.SetColumnWidth(6, 10 * 256); // OrderId

            // 设置标题行样式
            var headerStyle = workbook.CreateCellStyle();
            var headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            headerStyle.SetFont(headerFont);
            headerStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Grey25Percent.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;

            // 应用标题样式
            var headerRowObj = worksheet.GetRow(0);
            for (int i = 0; i < 7; i++)
            {
                headerRowObj.GetCell(i).CellStyle = headerStyle;
            }

            // 保存文件
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
            workbook.Write(fileStream);
            workbook.Close();
        }
    }
}
